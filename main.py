import os
import argparse
import torch
import numpy as np
from pathlib import Path
import yaml
import warnings

# 设置项目根路径
PROJECT_ROOT = Path(__file__).parent
import sys
sys.path.append(str(PROJECT_ROOT))

# 忽略PyTorch Geometric的警告
warnings.filterwarnings("ignore", message="An issue occurred while importing")

from config.base_config import ExperimentConfig
from src.data_generation.workflowsim_generator import WorkflowSimGenerator
from src.preprocessing.feature_extraction import WorkflowFeatureExtractor
from src.preprocessing.graph_coloring import ImprovedGraphColoring
from src.models.three_layer_gnn import ThreeLayerGNNScheduler
from src.training.trainer import GNNTrainer
from src.evaluation.metrics import SchedulingMetrics
from src.utils.logger import setup_logging, get_logger
from src.utils.reproducibility import set_random_seeds
from experiments.debug_layers import LayerDebugger
from src.evaluation.visualization import WorkflowVisualizer
from src.evaluation.enhanced_visualization import EnhancedWorkflowVisualizer
from src.data_generation.data_loader import create_data_loaders

def parse_args():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description='GNN工作流调度器')
    
    parser.add_argument('--config', type=str, default='config/experiment_config.yaml',
                       help='配置文件路径')
    parser.add_argument('--mode', type=str, choices=['generate', 'train', 'evaluate', 'debug', 'all'],
                       default='all', help='运行模式')
    parser.add_argument('--data-dir', type=str, default='./data',
                       help='数据目录')
    parser.add_argument('--output-dir', type=str, default='./outputs',
                       help='输出目录')
    parser.add_argument('--debug', action='store_true',
                       help='启用调试模式')
    parser.add_argument('--device', type=str, default='auto',
                       help='计算设备 (cpu/cuda/auto)')
    parser.add_argument('--seed', type=int, default=42,
                       help='随机种子')
    parser.add_argument('--check-env', action='store_true',
                       help='检查环境配置')
    
    return parser.parse_args()

def setup_device(device_str: str) -> torch.device:
    """设置计算设备"""
    if device_str == 'auto':
        device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    else:
        device = torch.device(device_str)
    
    print(f"🖥️  使用设备: {device}")
    if device.type == 'cuda':
        print(f"   GPU信息: {torch.cuda.get_device_name()}")
        print(f"   显存: {torch.cuda.get_device_properties(device).total_memory / 1e9:.1f}GB")
    
    return device

def create_default_config():
    """创建默认配置"""
    from config.base_config import DataConfig, ModelConfig, TrainingConfig, ExperimentConfig
    
    return ExperimentConfig(
        data=DataConfig(),
        model=ModelConfig(),
        training=TrainingConfig()
    )

def generate_data(config: ExperimentConfig, data_dir: str):
    """生成数据集"""
    print("📊 开始生成数据集...")
    
    # 创建数据生成器
    generator = WorkflowSimGenerator(config.data)
    
    # 生成数据集
    generator.generate_dataset(data_dir)
    
    print(f"✅ 数据集生成完成，保存在: {data_dir}")

def preprocess_data(data_dir: str, output_dir: str, debug: bool = False):
    """预处理数据"""
    print("🔄 开始数据预处理...")
    
    # 创建特征提取器
    feature_extractor = WorkflowFeatureExtractor()
    
    # 加载原始数据
    import json
    import networkx as nx
    
    workflow_file = os.path.join(data_dir, 'workflows.json')
    if not os.path.exists(workflow_file):
        print(f"❌ 找不到工作流数据文件: {workflow_file}")
        print("   请先运行数据生成: python main_fixed.py --mode generate")
        return None
    
    with open(workflow_file, 'r') as f:
        workflows_data = json.load(f)
    
    processed_data = []
    
    # 限制处理数量以避免内存问题
    max_workflows = 10 if debug else min(100, len(workflows_data))
    
    for i, workflow_data in enumerate(workflows_data[:max_workflows]):
        print(f"处理工作流 {i+1}/{max_workflows}")
        
        try:
            # 重构DAG
            dag = nx.node_link_graph(workflow_data['dag'])
            
            # 提取特征
            task_features, metadata = feature_extractor.extract_task_features(
                dag, workflow_data['type']
            )
            
            # 提取节点特征
            node_features = feature_extractor.extract_node_features(workflow_data['nodes'])
            
            # 保存处理后的数据
            processed_workflow = {
                'id': workflow_data['id'],
                'type': workflow_data['type'],
                'task_features': task_features.numpy(),
                'node_features': node_features.numpy(),
                'adjacency_matrix': nx.adjacency_matrix(dag).toarray(),
                'metadata': metadata,
                'original_data': workflow_data
            }
            
            processed_data.append(processed_workflow)
            
            # 调试模式下可视化第一个工作流的着色结果
            if debug and i == 0:
                coloring_result = metadata['coloring_result']
                graph_coloring = ImprovedGraphColoring()
                
                os.makedirs(output_dir, exist_ok=True)
                viz_path = os.path.join(output_dir, f'workflow_{i}_coloring.png')
                graph_coloring.visualize_coloring(dag, coloring_result, viz_path)
                print(f"   着色可视化保存到: {viz_path}")
                
        except Exception as e:
            print(f"   ⚠️ 处理工作流 {i} 时出错: {e}")
            continue
    
    if not processed_data:
        print("❌ 没有成功处理任何工作流")
        return None
    
    # 保存预处理数据
    os.makedirs(output_dir, exist_ok=True)
    
    # 保存为numpy格式（便于快速加载）
    np.save(os.path.join(output_dir, 'processed_workflows.npy'), processed_data)
    
    print(f"✅ 数据预处理完成，保存在: {output_dir}")
    print(f"   成功处理 {len(processed_data)} 个工作流")
    
    return processed_data

def train_model(config: ExperimentConfig, processed_data, device: torch.device, 
                output_dir: str, debug: bool = False):
    """训练模型"""
    print("🚀 开始模型训练...")
    
    # 创建模型
    model_config = {
        'input_dim': 128,  # 特征维度
        'hidden_dim': config.model.hidden_dim,
        'num_heads': config.model.num_heads,
        'num_transformer_layers': config.model.num_layers,
        'num_gat_layers': 3,
        'dropout': config.model.dropout,
        'constraint_weights': {
            'dependency': 1.0,
            'resource': 1.0,
            'temporal': 0.5,
            'communication': 0.5
        }
    }

    model = ThreeLayerGNNScheduler(model_config)

    # 创建训练器
    trainer = GNNTrainer(model, config.training, device)

    # 准备数据加载器（这里简化为直接处理，实际应该用DataLoader）
    # 为简化示例，这里只进行一个epoch的演示
    print("📚 准备训练数据...")

    if processed_data and len(processed_data) > 0:
        # 使用第一个工作流作为示例
        sample_workflow = processed_data[0]
        num_tasks = sample_workflow['task_features'].shape[0]
        num_nodes = sample_workflow['node_features'].shape[0]

        # 构造批次数据
        batch_data = {
            'task_features': torch.tensor(sample_workflow['task_features']).unsqueeze(0),
            'node_features': torch.tensor(sample_workflow['node_features']),
            'adjacency_matrix': torch.tensor(sample_workflow['adjacency_matrix']).float().unsqueeze(0),
            'task_edge_index': torch.tensor([[0, 1], [1, 2]]).T.long() if num_tasks > 2 else torch.empty((2, 0), dtype=torch.long),
            'task_batch': torch.zeros(num_tasks, dtype=torch.long),
            'node_batch': torch.zeros(num_nodes, dtype=torch.long),
            'resource_constraints': torch.randn(1, num_nodes, 4),
            'constraint_data': {
                'dependency': torch.randn(1, num_tasks, 256),
                'resource': torch.randn(1, num_nodes, 256),
                'adjacency_matrix': torch.tensor(sample_workflow['adjacency_matrix']).float().unsqueeze(0),
                'task_demands': torch.randn(1, num_tasks, 4),
                'node_capacities': torch.randn(1, num_nodes, 4),
            },
            'ground_truth_assignments': torch.randint(0, num_nodes, (1, num_tasks))
        }
        
        # 前向传播测试
        print("🧪 执行前向传播测试...")
        model.eval()
        with torch.no_grad():
            try:
                assignment_probs, debug_info = model(batch_data, debug_mode=True)
                
                print(f"✅ 前向传播成功!")
                print(f"   输出形状: {assignment_probs.shape}")
                print(f"   分配概率范围: [{assignment_probs.min().item():.4f}, {assignment_probs.max().item():.4f}]")
                print(f"   概率和验证: {assignment_probs.sum(dim=-1).mean().item():.4f} (应该接近1.0)")
                
                # 创建可视化器并生成图表
                print("🎨 生成可视化图表...")
                visualizer = WorkflowVisualizer(os.path.join(output_dir, 'visualizations'))
                enhanced_visualizer = EnhancedWorkflowVisualizer(os.path.join(output_dir, 'interactive'))

                # 1. 工作流DAG可视化
                visualizer.visualize_workflow_dag(sample_workflow['original_data'])

                # 2. 节点特征可视化
                visualizer.visualize_node_characteristics(batch_data['node_features'])

                # 3. 各层输出可视化
                visualizer.visualize_layer_outputs(debug_info, sample_workflow['original_data'])

                # 4. 最终分配结果可视化
                visualizer.visualize_final_assignment(assignment_probs, sample_workflow['original_data'])

                # 5. 综合结果可视化
                visualizer.visualize_comprehensive_results(debug_info, sample_workflow['original_data'], assignment_probs)

                # 6. 增强的交互式可视化
                print("🎨 生成交互式可视化...")
                try:
                    # 创建训练历史（模拟数据）
                    train_history = {
                        'losses': [{'total': 0.5}, {'total': 0.3}, {'total': 0.2}],
                        'val_losses': [{'total': 0.6}, {'total': 0.4}, {'total': 0.3}],
                        'lr': [1e-3, 8e-4, 6e-4],
                        'metrics': [{'makespan': 10.0}, {'makespan': 8.5}, {'makespan': 7.2}]
                    }

                    # 生成综合报告
                    report_path = enhanced_visualizer.generate_comprehensive_report(
                        sample_workflow['original_data'], debug_info, assignment_probs, train_history
                    )
                    print(f"📊 交互式综合报告生成完成: {report_path}")
                except Exception as e:
                    print(f"⚠️ 交互式可视化生成失败: {e}")

                print("✅ 所有可视化图表生成完成!")
                print(f"📁 静态图表保存在: {os.path.join(output_dir, 'visualizations')}")
                print(f"📁 交互式图表保存在: {os.path.join(output_dir, 'interactive')}")
                
                # 保存调试信息
                debug_output_dir = os.path.join(output_dir, 'debug')
                os.makedirs(debug_output_dir, exist_ok=True)
                
                # 使用层调试器
                layer_debugger = LayerDebugger(model)
                layer_debugger.debug_all_layers(batch_data, debug_output_dir)
                
            except Exception as e:
                print(f"❌ 前向传播失败: {e}")
                import traceback
                traceback.print_exc()
        
    else:
        print("⚠️  完整训练功能待实现（需要完整的数据加载器和训练循环）")
        print("   当前版本专注于架构验证和调试功能")
    
    print(f"✅ 模型训练阶段完成")

def evaluate_model(processed_data, output_dir: str):
    """评估模型"""
    print("📈 开始模型评估...")
    
    if processed_data is None or len(processed_data) == 0:
        print("❌ 没有可用的评估数据")
        return
    
    # 创建评估指标计算器
    metrics = SchedulingMetrics()
    
    # 模拟评估过程
    print("📊 计算性能指标...")
    
    # 使用第一个工作流作为示例
    sample_workflow = processed_data[0]
    
    # 创建模拟的分配结果
    num_tasks = sample_workflow['task_features'].shape[0]
    num_nodes = sample_workflow['node_features'].shape[0]
    
    # 随机分配（实际应该用训练好的模型预测）
    assignment = torch.zeros(1, num_tasks, num_nodes)
    for i in range(num_tasks):
        assigned_node = np.random.randint(0, num_nodes)
        assignment[0, i, assigned_node] = 1
    
    # 构造评估数据
    try:
        # 获取任务运行时间
        dag_nodes = sample_workflow['original_data']['dag']['nodes']
        task_runtimes = []
        for i in range(num_tasks):
            if i < len(dag_nodes):
                task_runtimes.append(dag_nodes[i].get('runtime', 1.0))
            else:
                task_runtimes.append(1.0)
        
        eval_data = {
            'task_runtimes': torch.tensor(task_runtimes).unsqueeze(0),
            'adjacency_matrix': torch.tensor(sample_workflow['adjacency_matrix']).unsqueeze(0),
            'node_capacities': torch.randn(1, num_nodes, 4),
            'task_demands': torch.randn(1, num_tasks, 4),
            'node_costs': torch.randn(1, num_nodes),
            'node_energy_efficiency': torch.randn(1, num_nodes),
        }
        
        # 计算指标
        batch_metrics = metrics.compute_batch_metrics(assignment, eval_data)
        
        print("📊 评估结果:")
        for metric_name, value in batch_metrics.items():
            print(f"   {metric_name}: {value:.4f}")
        
        # 保存评估结果
        eval_results = {
            'metrics': batch_metrics,
            'assignment': assignment.numpy().tolist(),
            'workflow_info': {
                'type': sample_workflow['type'],
                'num_tasks': num_tasks,
                'num_nodes': num_nodes
            }
        }
        
        import json
        os.makedirs(output_dir, exist_ok=True)
        with open(os.path.join(output_dir, 'evaluation_results.json'), 'w') as f:
            json.dump(eval_results, f, indent=2)
            
    except Exception as e:
        print(f"❌ 评估过程出错: {e}")
        import traceback
        traceback.print_exc()
    
    print(f"✅ 模型评估完成")

def main():
    """主函数"""
    args = parse_args()
    
    # 环境检查
    if args.check_env:
        check_pytorch_geometric()
        return
    
    # 设置随机种子
    set_random_seeds(args.seed)
    
    # 设置日志
    setup_logging(level='DEBUG' if args.debug else 'INFO')
    logger = get_logger(__name__)
    
    # 设置设备
    device = setup_device(args.device)
    
    # 创建输出目录
    os.makedirs(args.output_dir, exist_ok=True)
    
    # 加载配置
    config = None
    if os.path.exists(args.config):
        try:
            config = ExperimentConfig.from_yaml(args.config)
            logger.info(f"✅ 成功加载配置文件: {args.config}")
        except Exception as e:
            logger.warning(f"⚠️ 配置文件加载失败: {e}")
            logger.info("使用默认配置")
            config = create_default_config()
    else:
        logger.warning(f"⚠️ 配置文件 {args.config} 不存在，使用默认配置")
        config = create_default_config()
    
    logger.info("🎯 GNN工作流调度器启动")
    logger.info(f"   运行模式: {args.mode}")
    logger.info(f"   数据目录: {args.data_dir}")
    logger.info(f"   输出目录: {args.output_dir}")
    logger.info(f"   调试模式: {args.debug}")
    
    processed_data = None
    
    # 执行不同模式
    try:
        if args.mode in ['generate', 'all']:
            generate_data(config, args.data_dir)
        
        if args.mode in ['train', 'all']:
            # 先进行数据预处理
            processed_file = os.path.join(args.output_dir, 'processed_workflows.npy')
            if not os.path.exists(processed_file):
                processed_data = preprocess_data(args.data_dir, args.output_dir, args.debug)
            else:
                try:
                    processed_data = np.load(processed_file, allow_pickle=True).tolist()
                    logger.info(f"✅ 加载已处理的数据: {len(processed_data)} 个工作流")
                except Exception as e:
                    logger.warning(f"⚠️ 加载预处理数据失败: {e}")
                    processed_data = preprocess_data(args.data_dir, args.output_dir, args.debug)
            
            if processed_data:
                train_model(config, processed_data, device, args.output_dir, args.debug)
        
        if args.mode in ['evaluate', 'all']:
            if processed_data is None:
                processed_file = os.path.join(args.output_dir, 'processed_workflows.npy')
                if os.path.exists(processed_file):
                    processed_data = np.load(processed_file, allow_pickle=True).tolist()
            
            if processed_data:
                evaluate_model(processed_data, args.output_dir)
        
        if args.mode == 'debug':
            logger.info("🐛 进入调试模式...")
            processed_file = os.path.join(args.output_dir, 'processed_workflows.npy')
            if not os.path.exists(processed_file):
                processed_data = preprocess_data(args.data_dir, args.output_dir, debug=True)
            else:
                processed_data = np.load(processed_file, allow_pickle=True).tolist()
            
            if processed_data:
                train_model(config, processed_data, device, args.output_dir, debug=True)
        
    except Exception as e:
        logger.error(f"❌ 程序执行出错: {e}")
        import traceback
        traceback.print_exc()
        return
    
    logger.info("🎉 程序执行完成!")

if __name__ == '__main__':
    main()
