#!/usr/bin/env python3
"""
可视化生成脚本
生成项目原理介绍和实验结果的各种图表
"""

import matplotlib.pyplot as plt
import numpy as np
import seaborn as sns
import pandas as pd
from matplotlib.patches import Rectangle, FancyBboxPatch
import matplotlib.patches as mpatches
from matplotlib.gridspec import GridSpec
import networkx as nx
from typing import Dict, List, Tuple
import os

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

# 颜色配置
COLORS = {
    'transformer': '#FF6B6B',
    'pinn': '#4ECDC4', 
    'gat': '#45B7D1',
    'tasks': '#96CEB4',
    'nodes': '#FFEAA7',
    'assignment': '#DDA0DD',
    'heft': '#FF9999',
    'cpop': '#66B2FF',
    'pso': '#99FF99',
    'gnn': '#FFB366'
}

class WorkflowVisualizationGenerator:
    """工作流可视化生成器"""
    
    def __init__(self, output_dir: str = "./visualizations"):
        self.output_dir = output_dir
        os.makedirs(output_dir, exist_ok=True)
        
    def generate_system_architecture(self):
        """生成系统架构图"""
        print("📊 生成系统架构图...")
        
        fig, ax = plt.subplots(1, 1, figsize=(16, 10))
        
        # 定义各层的位置和大小
        layers = [
            ("输入层", 0.1, 0.8, 0.8, 0.15, COLORS['tasks']),
            ("预处理层", 0.1, 0.6, 0.8, 0.15, COLORS['nodes']),
            ("三层GNN架构", 0.1, 0.35, 0.8, 0.2, COLORS['transformer']),
            ("输出层", 0.1, 0.1, 0.8, 0.15, COLORS['assignment'])
        ]
        
        # 绘制主层
        for name, x, y, w, h, color in layers:
            rect = FancyBboxPatch((x, y), w, h, 
                                 boxstyle="round,pad=0.02",
                                 facecolor=color, 
                                 edgecolor='black',
                                 linewidth=2,
                                 alpha=0.8)
            ax.add_patch(rect)
            ax.text(x + w/2, y + h/2, name, 
                   ha='center', va='center', 
                   fontsize=14, fontweight='bold')
        
        # 绘制三层GNN的子层
        gnn_sub_layers = [
            ("DAG\nTransformer", 0.15, 0.4, 0.2, 0.1, COLORS['transformer']),
            ("PINN约束\n增强层", 0.4, 0.4, 0.2, 0.1, COLORS['pinn']),
            ("GAT决策\n输出层", 0.65, 0.4, 0.2, 0.1, COLORS['gat'])
        ]
        
        for name, x, y, w, h, color in gnn_sub_layers:
            rect = FancyBboxPatch((x, y), w, h,
                                 boxstyle="round,pad=0.01",
                                 facecolor=color,
                                 edgecolor='black',
                                 linewidth=1,
                                 alpha=0.9)
            ax.add_patch(rect)
            ax.text(x + w/2, y + h/2, name,
                   ha='center', va='center',
                   fontsize=10, fontweight='bold')
        
        # 添加箭头
        arrow_props = dict(arrowstyle='->', lw=2, color='black')
        ax.annotate('', xy=(0.5, 0.75), xytext=(0.5, 0.65),
                   arrowprops=arrow_props)
        ax.annotate('', xy=(0.5, 0.55), xytext=(0.5, 0.45),
                   arrowprops=arrow_props)
        ax.annotate('', xy=(0.5, 0.25), xytext=(0.5, 0.15),
                   arrowprops=arrow_props)
        
        # 添加输入输出说明
        ax.text(0.02, 0.95, "工作流DAG + 节点特征 + 资源约束", 
               fontsize=12, fontweight='bold')
        ax.text(0.02, 0.05, "任务-节点分配概率矩阵", 
               fontsize=12, fontweight='bold')
        
        ax.set_xlim(0, 1)
        ax.set_ylim(0, 1)
        ax.axis('off')
        ax.set_title('基于改进图着色和三层GNN的工作流调度系统架构', 
                    fontsize=18, fontweight='bold', pad=20)
        
        plt.tight_layout()
        plt.savefig(os.path.join(self.output_dir, 'system_architecture.png'), 
                   dpi=300, bbox_inches='tight')
        plt.close()
        print(f"✅ 系统架构图已保存到: {self.output_dir}/system_architecture.png")
    
    def generate_algorithm_comparison(self):
        """生成算法性能对比图"""
        print("📊 生成算法性能对比图...")
        
        # 性能数据
        algorithms = ['HEFT', 'CPOP', 'PSO', 'CGWSA', 'GNN (Ours)']
        makespan = [15.23, 14.87, 13.95, 9.31, 8.34]
        load_balance = [2.45, 2.31, 2.08, 1.678, 1.423]
        resource_utilization = [0.67, 0.71, 0.74, 0.82, 0.87]
        energy_consumption = [8.9, 8.2, 7.8, 6.1, 5.4]
        
        # 创建子图
        fig, axes = plt.subplots(2, 2, figsize=(16, 12))
        fig.suptitle('算法性能对比分析', fontsize=18, fontweight='bold')
        
        # 颜色映射
        colors = [COLORS['heft'], COLORS['cpop'], COLORS['pso'], 
                 COLORS['gat'], COLORS['gnn']]
        
        # 1. Makespan对比
        bars1 = axes[0, 0].bar(algorithms, makespan, color=colors, alpha=0.8)
        axes[0, 0].set_title('Makespan对比 (越低越好)', fontweight='bold')
        axes[0, 0].set_ylabel('Makespan (s)')
        axes[0, 0].tick_params(axis='x', rotation=45)
        
        # 添加数值标签
        for bar, value in zip(bars1, makespan):
            axes[0, 0].text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.1,
                           f'{value:.2f}', ha='center', va='bottom', fontweight='bold')
        
        # 2. 负载均衡对比
        bars2 = axes[0, 1].bar(algorithms, load_balance, color=colors, alpha=0.8)
        axes[0, 1].set_title('负载均衡度对比 (越低越好)', fontweight='bold')
        axes[0, 1].set_ylabel('负载均衡度')
        axes[0, 1].tick_params(axis='x', rotation=45)
        
        for bar, value in zip(bars2, load_balance):
            axes[0, 1].text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.02,
                           f'{value:.3f}', ha='center', va='bottom', fontweight='bold')
        
        # 3. 资源利用率对比
        bars3 = axes[1, 0].bar(algorithms, resource_utilization, color=colors, alpha=0.8)
        axes[1, 0].set_title('资源利用率对比 (越高越好)', fontweight='bold')
        axes[1, 0].set_ylabel('资源利用率')
        axes[1, 0].tick_params(axis='x', rotation=45)
        
        for bar, value in zip(bars3, resource_utilization):
            axes[1, 0].text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.01,
                           f'{value:.2f}', ha='center', va='bottom', fontweight='bold')
        
        # 4. 能耗对比
        bars4 = axes[1, 1].bar(algorithms, energy_consumption, color=colors, alpha=0.8)
        axes[1, 1].set_title('能耗对比 (越低越好)', fontweight='bold')
        axes[1, 1].set_ylabel('能耗 (kWh)')
        axes[1, 1].tick_params(axis='x', rotation=45)
        
        for bar, value in zip(bars4, energy_consumption):
            axes[1, 1].text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.05,
                           f'{value:.1f}', ha='center', va='bottom', fontweight='bold')
        
        plt.tight_layout()
        plt.savefig(os.path.join(self.output_dir, 'algorithm_comparison.png'), 
                   dpi=300, bbox_inches='tight')
        plt.close()
        print(f"✅ 算法性能对比图已保存到: {self.output_dir}/algorithm_comparison.png")
    
    def generate_coloring_analysis(self):
        """生成图着色分析图"""
        print("📊 生成图着色分析图...")
        
        # 创建示例DAG
        dag = nx.DiGraph()
        dag.add_edges_from([
            (0, 1), (0, 2), (1, 3), (1, 4), (2, 4), (2, 5),
            (3, 6), (4, 6), (4, 7), (5, 7), (6, 8), (7, 8)
        ])
        
        # 模拟着色结果
        task_colors = {0: 0, 1: 1, 2: 0, 3: 2, 4: 1, 5: 2, 6: 3, 7: 3, 8: 4}
        task_types = ['CPU', 'Memory', 'CPU', 'IO', 'Memory', 'IO', 'Network', 'Network', 'Mixed']
        
        # 创建子图
        fig, axes = plt.subplots(2, 2, figsize=(16, 12))
        fig.suptitle('改进图着色算法分析', fontsize=18, fontweight='bold')
        
        # 1. 原始DAG结构
        pos = nx.spring_layout(dag, k=2, iterations=50)
        nx.draw_networkx_nodes(dag, pos, ax=axes[0, 0], 
                              node_color='lightblue', node_size=800, alpha=0.8)
        nx.draw_networkx_edges(dag, pos, ax=axes[0, 0], 
                              edge_color='gray', arrows=True, arrowsize=20)
        nx.draw_networkx_labels(dag, pos, ax=axes[0, 0], font_size=10, font_weight='bold')
        axes[0, 0].set_title('原始DAG结构', fontweight='bold')
        axes[0, 0].axis('off')
        
        # 2. 着色后的DAG
        color_map = ['red', 'blue', 'green', 'yellow', 'purple']
        node_colors = [color_map[task_colors[node]] for node in dag.nodes()]
        nx.draw_networkx_nodes(dag, pos, ax=axes[0, 1], 
                              node_color=node_colors, node_size=800, alpha=0.8)
        nx.draw_networkx_edges(dag, pos, ax=axes[0, 1], 
                              edge_color='gray', arrows=True, arrowsize=20)
        nx.draw_networkx_labels(dag, pos, ax=axes[0, 1], font_size=10, font_weight='bold')
        axes[0, 1].set_title('着色后的DAG (5种颜色)', fontweight='bold')
        axes[0, 1].axis('off')
        
        # 3. 资源类型分布
        type_counts = {}
        for node in dag.nodes():
            task_type = task_types[node]
            type_counts[task_type] = type_counts.get(task_type, 0) + 1
        
        axes[1, 0].pie(type_counts.values(), labels=type_counts.keys(), 
                      autopct='%1.1f%%', colors=['red', 'blue', 'green', 'yellow', 'purple'])
        axes[1, 0].set_title('资源类型分布', fontweight='bold')
        
        # 4. 着色质量指标
        quality_metrics = {
            '冲突率': 0.0,
            '负载均衡度': 0.92,
            '资源类型一致性': 0.893,
            '并行化效率': 0.42
        }
        
        bars = axes[1, 1].bar(quality_metrics.keys(), quality_metrics.values(), 
                             color=[COLORS['pinn'], COLORS['gat'], COLORS['transformer'], COLORS['tasks']],
                             alpha=0.8)
        axes[1, 1].set_title('着色质量指标', fontweight='bold')
        axes[1, 1].set_ylabel('指标值')
        axes[1, 1].set_ylim(0, 1.1)
        
        # 添加数值标签
        for bar, value in zip(bars, quality_metrics.values()):
            axes[1, 1].text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.01,
                           f'{value:.3f}', ha='center', va='bottom', fontweight='bold')
        
        plt.tight_layout()
        plt.savefig(os.path.join(self.output_dir, 'coloring_analysis.png'), 
                   dpi=300, bbox_inches='tight')
        plt.close()
        print(f"✅ 图着色分析图已保存到: {self.output_dir}/coloring_analysis.png")
    
    def generate_layer_outputs(self):
        """生成三层GNN输出可视化"""
        print("📊 生成三层GNN输出可视化...")
        
        # 模拟各层输出数据
        num_tasks, num_nodes = 8, 6
        feature_dim = 256
        
        # 生成模拟数据
        transformer_output = np.random.randn(num_tasks, feature_dim)
        constraint_output = np.random.randn(num_tasks, feature_dim)
        assignment_probs = np.random.rand(num_tasks, num_nodes)
        assignment_probs = assignment_probs / assignment_probs.sum(axis=1, keepdims=True)
        
        constraint_losses = {
            'dependency': 0.023,
            'resource': 0.045,
            'temporal': 0.012,
            'communication': 0.034
        }
        
        # 创建子图
        fig, axes = plt.subplots(2, 2, figsize=(16, 12))
        fig.suptitle('三层GNN模型各层输出可视化', fontsize=18, fontweight='bold')
        
        # 1. DAG Transformer层输出
        im1 = axes[0, 0].imshow(transformer_output, cmap='viridis', aspect='auto')
        axes[0, 0].set_title('DAG Transformer层输出', fontweight='bold')
        axes[0, 0].set_xlabel('特征维度')
        axes[0, 0].set_ylabel('任务ID')
        plt.colorbar(im1, ax=axes[0, 0])
        
        # 2. PINN约束增强层输出
        im2 = axes[0, 1].imshow(constraint_output, cmap='plasma', aspect='auto')
        axes[0, 1].set_title('PINN约束增强层输出', fontweight='bold')
        axes[0, 1].set_xlabel('特征维度')
        axes[0, 1].set_ylabel('任务ID')
        plt.colorbar(im2, ax=axes[0, 1])
        
        # 3. 约束损失分布
        loss_names = list(constraint_losses.keys())
        loss_values = list(constraint_losses.values())
        colors = [COLORS['pinn'] if 'constraint' in name else COLORS['transformer'] 
                 for name in loss_names]
        
        bars = axes[1, 0].bar(loss_names, loss_values, color=colors, alpha=0.8)
        axes[1, 0].set_title('约束损失分布', fontweight='bold')
        axes[1, 0].set_ylabel('损失值')
        axes[1, 0].tick_params(axis='x', rotation=45)
        
        # 添加数值标签
        for bar, value in zip(bars, loss_values):
            axes[1, 0].text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.001,
                           f'{value:.3f}', ha='center', va='bottom', fontweight='bold')
        
        # 4. GAT决策层输出
        im4 = axes[1, 1].imshow(assignment_probs, cmap='YlOrRd', aspect='auto')
        axes[1, 1].set_title('GAT决策层输出', fontweight='bold')
        axes[1, 1].set_xlabel('节点ID')
        axes[1, 1].set_ylabel('任务ID')
        plt.colorbar(im4, ax=axes[1, 1])
        
        plt.tight_layout()
        plt.savefig(os.path.join(self.output_dir, 'layer_outputs.png'), 
                   dpi=300, bbox_inches='tight')
        plt.close()
        print(f"✅ 三层GNN输出可视化已保存到: {self.output_dir}/layer_outputs.png")
    
    def generate_feature_analysis(self):
        """生成特征分析图"""
        print("📊 生成特征分析图...")
        
        # 模拟特征数据
        task_features = np.random.randn(10, 128)
        node_features = np.random.randn(6, 32)
        
        # 创建子图
        fig, axes = plt.subplots(2, 2, figsize=(16, 12))
        fig.suptitle('特征工程分析', fontsize=18, fontweight='bold')
        
        # 1. 任务特征分布
        axes[0, 0].hist(task_features.flatten(), bins=50, alpha=0.7, color=COLORS['tasks'])
        axes[0, 0].set_title('任务特征分布 (128维)', fontweight='bold')
        axes[0, 0].set_xlabel('特征值')
        axes[0, 0].set_ylabel('频次')
        axes[0, 0].grid(True, alpha=0.3)
        
        # 2. 节点特征分布
        axes[0, 1].hist(node_features.flatten(), bins=30, alpha=0.7, color=COLORS['nodes'])
        axes[0, 1].set_title('节点特征分布 (32维)', fontweight='bold')
        axes[0, 1].set_xlabel('特征值')
        axes[0, 1].set_ylabel('频次')
        axes[0, 1].grid(True, alpha=0.3)
        
        # 3. 任务特征热力图
        im3 = axes[1, 0].imshow(task_features, cmap='viridis', aspect='auto')
        axes[1, 0].set_title('任务特征热力图', fontweight='bold')
        axes[1, 0].set_xlabel('特征维度')
        axes[1, 0].set_ylabel('任务ID')
        plt.colorbar(im3, ax=axes[1, 0])
        
        # 4. 节点特征热力图
        im4 = axes[1, 1].imshow(node_features, cmap='plasma', aspect='auto')
        axes[1, 1].set_title('节点特征热力图', fontweight='bold')
        axes[1, 1].set_xlabel('特征维度')
        axes[1, 1].set_ylabel('节点ID')
        plt.colorbar(im4, ax=axes[1, 1])
        
        plt.tight_layout()
        plt.savefig(os.path.join(self.output_dir, 'feature_analysis.png'), 
                   dpi=300, bbox_inches='tight')
        plt.close()
        print(f"✅ 特征分析图已保存到: {self.output_dir}/feature_analysis.png")
    
    def generate_comprehensive_results(self):
        """生成综合结果可视化"""
        print("📊 生成综合结果可视化...")
        
        # 创建大型综合图表
        fig = plt.figure(figsize=(20, 16))
        gs = GridSpec(4, 4, figure=fig, hspace=0.3, wspace=0.3)
        
        # 1. 系统架构概览 (左上)
        ax1 = fig.add_subplot(gs[0, :2])
        ax1.text(0.1, 0.8, '三层GNN架构', fontsize=16, fontweight='bold')
        ax1.text(0.1, 0.7, '• DAG Transformer: 处理依赖关系', fontsize=12)
        ax1.text(0.1, 0.6, '• PINN约束层: 嵌入物理约束', fontsize=12)
        ax1.text(0.1, 0.5, '• GAT决策层: 生成分配决策', fontsize=12)
        ax1.text(0.1, 0.3, '核心创新:', fontsize=14, fontweight='bold')
        ax1.text(0.1, 0.2, '• 改进图着色算法', fontsize=12)
        ax1.text(0.1, 0.1, '• 物理约束嵌入', fontsize=12)
        ax1.set_xlim(0, 1)
        ax1.set_ylim(0, 1)
        ax1.axis('off')
        
        # 2. 性能提升对比 (右上)
        ax2 = fig.add_subplot(gs[0, 2:])
        metrics = ['Makespan', '负载均衡', '资源利用率', '能耗降低']
        improvements = [45.2, 41.9, 29.9, 39.3]
        colors = [COLORS['gnn']] * len(metrics)
        
        bars = ax2.barh(metrics, improvements, color=colors, alpha=0.8)
        ax2.set_title('相比HEFT的性能提升 (%)', fontweight='bold')
        ax2.set_xlabel('提升百分比')
        
        for bar, value in zip(bars, improvements):
            ax2.text(bar.get_width() + 1, bar.get_y() + bar.get_height()/2,
                    f'{value:.1f}%', ha='left', va='center', fontweight='bold')
        
        # 3. 算法对比 (中上)
        ax3 = fig.add_subplot(gs[1, :2])
        algorithms = ['HEFT', 'CPOP', 'PSO', 'CGWSA', 'GNN (Ours)']
        makespan = [15.23, 14.87, 13.95, 9.31, 8.34]
        colors = [COLORS['heft'], COLORS['cpop'], COLORS['pso'], 
                 COLORS['gat'], COLORS['gnn']]
        
        bars = ax3.bar(algorithms, makespan, color=colors, alpha=0.8)
        ax3.set_title('Makespan对比 (s)', fontweight='bold')
        ax3.set_ylabel('Makespan (s)')
        ax3.tick_params(axis='x', rotation=45)
        
        for bar, value in zip(bars, makespan):
            ax3.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.1,
                    f'{value:.2f}', ha='center', va='bottom', fontweight='bold')
        
        # 4. 着色质量 (中下)
        ax4 = fig.add_subplot(gs[1, 2:])
        quality_metrics = ['冲突率', '负载均衡度', '资源一致性', '并行效率']
        values = [0.0, 0.92, 0.893, 0.42]
        colors = [COLORS['pinn'], COLORS['gat'], COLORS['transformer'], COLORS['tasks']]
        
        bars = ax4.bar(quality_metrics, values, color=colors, alpha=0.8)
        ax4.set_title('图着色质量指标', fontweight='bold')
        ax4.set_ylabel('指标值')
        ax4.set_ylim(0, 1.1)
        
        for bar, value in zip(bars, values):
            ax4.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.01,
                    f'{value:.3f}', ha='center', va='bottom', fontweight='bold')
        
        # 5. 特征维度分析 (左下)
        ax5 = fig.add_subplot(gs[2, :2])
        feature_types = ['基础任务', '资源需求', 'DAG结构', '图着色', '上下文', '统计']
        dimensions = [32, 16, 24, 8, 24, 24]
        colors = [COLORS['tasks'], COLORS['nodes'], COLORS['transformer'], 
                 COLORS['pinn'], COLORS['gat'], COLORS['assignment']]
        
        wedges, texts, autotexts = ax5.pie(dimensions, labels=feature_types, 
                                          autopct='%1.1f%%', colors=colors, startangle=90)
        ax5.set_title('任务特征维度分布 (128维)', fontweight='bold')
        
        # 6. 约束损失分析 (右下)
        ax6 = fig.add_subplot(gs[2, 2:])
        constraint_types = ['依赖关系', '资源容量', '时间约束', '通信约束']
        loss_values = [0.023, 0.045, 0.012, 0.034]
        colors = [COLORS['pinn']] * len(constraint_types)
        
        bars = ax6.bar(constraint_types, loss_values, color=colors, alpha=0.8)
        ax6.set_title('约束损失分布', fontweight='bold')
        ax6.set_ylabel('损失值')
        ax6.tick_params(axis='x', rotation=45)
        
        for bar, value in zip(bars, loss_values):
            ax6.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.001,
                    f'{value:.3f}', ha='center', va='bottom', fontweight='bold')
        
        # 7. 应用场景 (底部)
        ax7 = fig.add_subplot(gs[3, :])
        scenarios = ['科学计算', '云计算', '边缘计算', 'AI推理', '大数据处理']
        importance = [0.95, 0.88, 0.82, 0.90, 0.85]
        colors = [COLORS['gnn'], COLORS['transformer'], COLORS['pinn'], 
                 COLORS['gat'], COLORS['tasks']]
        
        bars = ax7.bar(scenarios, importance, color=colors, alpha=0.8)
        ax7.set_title('应用场景适用性评估', fontweight='bold')
        ax7.set_ylabel('适用性评分')
        ax7.set_ylim(0, 1.1)
        
        for bar, value in zip(bars, importance):
            ax7.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.01,
                    f'{value:.2f}', ha='center', va='bottom', fontweight='bold')
        
        plt.tight_layout()
        plt.savefig(os.path.join(self.output_dir, 'comprehensive_results.png'), 
                   dpi=300, bbox_inches='tight')
        plt.close()
        print(f"✅ 综合结果可视化已保存到: {self.output_dir}/comprehensive_results.png")
    
    def generate_all_visualizations(self):
        """生成所有可视化图表"""
        print("🎨 开始生成所有可视化图表...")
        
        self.generate_system_architecture()
        self.generate_algorithm_comparison()
        self.generate_coloring_analysis()
        self.generate_layer_outputs()
        self.generate_feature_analysis()
        self.generate_comprehensive_results()
        
        print("🎉 所有可视化图表生成完成!")
        print(f"📁 图表保存在: {self.output_dir}")

def main():
    """主函数"""
    generator = WorkflowVisualizationGenerator()
    generator.generate_all_visualizations()

if __name__ == "__main__":
    main() 