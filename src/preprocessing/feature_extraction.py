import torch
import numpy as np
import networkx as nx
from typing import Dict, <PERSON><PERSON>, Any

class WorkflowFeatureExtractor:
    """工作流特征提取器"""
    
    def __init__(self):
        self.feature_dim = 128  # 特征维度
        
    def extract_task_features(self, dag: nx.DiGraph, workflow_type: str) -> Tuple[torch.Tensor, Dict]:
        """提取任务特征"""
        num_tasks = len(dag.nodes())
        
        # 创建任务特征矩阵
        task_features = torch.zeros(num_tasks, self.feature_dim)
        
        for i, node in enumerate(dag.nodes()):
            node_data = dag.nodes[node]
            
            # 基础特征
            features = []
            features.extend([
                node_data.get('cpu_demand', 0.0),
                node_data.get('memory_demand', 0.0),
                node_data.get('io_demand', 0.0),
                node_data.get('network_demand', 0.0),
                node_data.get('runtime', 0.0),
                node_data.get('input_size', 0.0),
                node_data.get('output_size', 0.0)
            ])
            
            # 图结构特征
            in_degree = dag.in_degree(node)
            out_degree = dag.out_degree(node)
            features.extend([in_degree, out_degree])
            
            # 填充到特征向量
            for j, feat in enumerate(features):
                if j < self.feature_dim:
                    task_features[i, j] = feat
        
        # 元数据
        metadata = {
            'num_tasks': num_tasks,
            'workflow_type': workflow_type,
            'coloring_result': {}  # 占位符
        }
        
        return task_features, metadata
    
    def extract_node_features(self, nodes: list) -> torch.Tensor:
        """提取节点特征"""
        num_nodes = len(nodes)
        node_features = torch.zeros(num_nodes, self.feature_dim)
        
        for i, node_data in enumerate(nodes):
            features = [
                node_data.get('cpu_capacity', 0.0),
                node_data.get('memory_capacity', 0.0),
                node_data.get('io_capacity', 0.0),
                node_data.get('network_capacity', 0.0),
                node_data.get('energy_efficiency', 0.0),
                node_data.get('cost_per_hour', 0.0)
            ]
            
            # 填充到特征向量
            for j, feat in enumerate(features):
                if j < self.feature_dim:
                    node_features[i, j] = feat
        
        return node_features